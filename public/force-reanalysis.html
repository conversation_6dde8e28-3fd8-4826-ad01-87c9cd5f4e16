<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>强制重新分析项目</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        button {
            background-color: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #40a9ff;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .log-output {
            background-color: #f8f8f8;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 500px;
            overflow-y: auto;
            margin-top: 10px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }
        .error {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }
        .warning {
            background-color: #fffbe6;
            border: 1px solid #ffe58f;
            color: #faad14;
        }
        .progress {
            width: 100%;
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar {
            height: 100%;
            background-color: #1890ff;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>强制重新分析项目</h1>
        <p>此页面将强制重新分析项目以应用储能收益计算的修复</p>
        
        <div>
            <button onclick="forceReanalysis()" id="reanalyzeBtn">强制重新分析</button>
            <button onclick="checkBefore()" id="checkBeforeBtn">检查修复前数据</button>
            <button onclick="checkAfter()" id="checkAfterBtn">检查修复后数据</button>
            <button onclick="clearLogs()">清除日志</button>
        </div>

        <div id="status"></div>
        
        <div id="progress" class="progress" style="display: none;">
            <div id="progressBar" class="progress-bar" style="width: 0%;"></div>
        </div>

        <div id="logOutput" class="log-output">等待操作...</div>
    </div>

    <script>
        const projectId = '4d450b50-49e1-48dc-a04e-4cf81aa7268e';
        let logOutput = document.getElementById('logOutput');
        let statusDiv = document.getElementById('status');
        let progressDiv = document.getElementById('progress');
        let progressBar = document.getElementById('progressBar');

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logOutput.textContent += `[${timestamp}] ${message}\n`;
            logOutput.scrollTop = logOutput.scrollHeight;
            console.log(message);
        }

        function setStatus(message, type = 'info') {
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function setProgress(percent) {
            progressBar.style.width = percent + '%';
            if (percent > 0) {
                progressDiv.style.display = 'block';
            } else {
                progressDiv.style.display = 'none';
            }
        }

        function clearLogs() {
            logOutput.textContent = '';
            statusDiv.innerHTML = '';
            setProgress(0);
        }

        async function getProjectData() {
            const response = await fetch(`/api/projects`);
            const result = await response.json();
            
            if (!result.success) {
                throw new Error('获取项目列表失败');
            }

            const project = result.data.items.find(p => p.id === projectId);
            if (!project) {
                throw new Error('项目不存在');
            }

            return project;
        }

        async function checkBefore() {
            try {
                log('检查修复前的储能收益数据...');
                setStatus('正在检查修复前数据...', 'warning');

                const project = await getProjectData();
                const storageBenefit = project.analysisResults?.yearlyData?.storageBenefit;
                
                log(`当前储能收益: ${storageBenefit} 日元`);
                
                if (Math.abs(storageBenefit - 96916.541) < 1) {
                    setStatus('❌ 确认：储能收益为错误值 (96916.541)，需要重新分析', 'error');
                    log('确认需要重新分析');
                } else if (Math.abs(storageBenefit - 385799.4) < 1) {
                    setStatus('✅ 储能收益已经是正确值 (385799.4)', 'success');
                    log('储能收益已经正确');
                } else {
                    setStatus(`⚠️ 储能收益为未知值: ${storageBenefit}`, 'warning');
                    log(`储能收益为未知值: ${storageBenefit}`);
                }

            } catch (error) {
                log(`检查失败: ${error.message}`);
                setStatus(`检查失败: ${error.message}`, 'error');
            }
        }

        async function checkAfter() {
            try {
                log('检查修复后的储能收益数据...');
                setStatus('正在检查修复后数据...', 'warning');

                const project = await getProjectData();
                const storageBenefit = project.analysisResults?.yearlyData?.storageBenefit;
                
                log(`当前储能收益: ${storageBenefit} 日元`);
                
                // 验证数据一致性
                const hourlyData = project.analysisResults?.hourlyData || [];
                let calculatedStorageBenefit = 0;
                
                for (const hour of hourlyData) {
                    let hourlyBenefit = 0;
                    if (hour.storageCharge > 0) {
                        hourlyBenefit = -hour.gridFeedInPrice * hour.storageCharge;
                    } else if (hour.storageCharge < 0) {
                        hourlyBenefit = hour.electricityPrice * Math.abs(hour.storageCharge);
                    }
                    calculatedStorageBenefit += hourlyBenefit;
                }
                
                log(`重新计算的储能收益: ${calculatedStorageBenefit.toFixed(2)} 日元`);
                
                const difference = Math.abs(storageBenefit - calculatedStorageBenefit);
                log(`差异: ${difference.toFixed(2)} 日元`);
                
                if (difference <= 1.0) {
                    setStatus('✅ 储能收益数据一致，修复成功！', 'success');
                    log('✅ 数据一致性验证通过');
                } else {
                    setStatus('❌ 储能收益数据不一致，需要进一步检查', 'error');
                    log('❌ 数据一致性验证失败');
                }

            } catch (error) {
                log(`检查失败: ${error.message}`);
                setStatus(`检查失败: ${error.message}`, 'error');
            }
        }

        async function forceReanalysis() {
            try {
                const reanalyzeBtn = document.getElementById('reanalyzeBtn');
                reanalyzeBtn.disabled = true;
                reanalyzeBtn.textContent = '分析中...';

                log('=== 开始强制重新分析项目 ===');
                setStatus('正在强制重新分析项目，请稍候...', 'warning');
                setProgress(10);

                // 获取项目数据
                log('步骤 1/5: 获取项目数据...');
                const project = await getProjectData();
                log(`找到项目: ${project.name}`);
                setProgress(20);

                // 检查当前储能收益
                const currentStorageBenefit = project.analysisResults?.yearlyData?.storageBenefit;
                log(`当前储能收益: ${currentStorageBenefit} 日元`);
                setProgress(30);

                // 模拟重新计算储能收益
                log('步骤 2/5: 重新计算储能收益...');
                const hourlyData = project.analysisResults?.hourlyData || [];
                let newStorageBenefit = 0;
                
                for (const hour of hourlyData) {
                    let hourlyBenefit = 0;
                    if (hour.storageCharge > 0) {
                        hourlyBenefit = -hour.gridFeedInPrice * hour.storageCharge;
                    } else if (hour.storageCharge < 0) {
                        hourlyBenefit = hour.electricityPrice * Math.abs(hour.storageCharge);
                    }
                    newStorageBenefit += hourlyBenefit;
                }
                
                log(`重新计算的储能收益: ${newStorageBenefit.toFixed(2)} 日元`);
                setProgress(50);

                // 更新项目数据
                log('步骤 3/5: 更新项目数据...');
                project.analysisResults.yearlyData.storageBenefit = newStorageBenefit;
                project.analysisResults.yearlyData.totalBenefit = 
                    project.analysisResults.yearlyData.pvBenefit + newStorageBenefit;
                project.analysisResults.yearlyData.renewableEnergyBenefit = 
                    project.analysisResults.yearlyData.pvBenefit + newStorageBenefit;
                
                setProgress(70);

                // 保存到服务器
                log('步骤 4/5: 保存到服务器...');
                const updateResponse = await fetch(`/api/projects/${projectId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(project)
                });

                if (!updateResponse.ok) {
                    throw new Error('保存项目数据失败');
                }

                setProgress(90);

                // 验证更新结果
                log('步骤 5/5: 验证更新结果...');
                const updatedProject = await getProjectData();
                const finalStorageBenefit = updatedProject.analysisResults?.yearlyData?.storageBenefit;
                
                log(`最终储能收益: ${finalStorageBenefit} 日元`);
                setProgress(100);

                if (Math.abs(finalStorageBenefit - newStorageBenefit) < 1) {
                    setStatus('✅ 项目重新分析完成，储能收益已修复！', 'success');
                    log('✅ 重新分析成功完成');
                } else {
                    setStatus('⚠️ 重新分析完成，但数据可能未正确保存', 'warning');
                    log('⚠️ 数据保存可能有问题');
                }

                setTimeout(() => {
                    setProgress(0);
                }, 2000);

            } catch (error) {
                log(`重新分析失败: ${error.message}`);
                setStatus(`重新分析失败: ${error.message}`, 'error');
                setProgress(0);
            } finally {
                const reanalyzeBtn = document.getElementById('reanalyzeBtn');
                reanalyzeBtn.disabled = false;
                reanalyzeBtn.textContent = '强制重新分析';
            }
        }

        // 页面加载时自动检查当前状态
        window.addEventListener('load', function() {
            log('页面加载完成');
            setTimeout(checkBefore, 1000);
        });
    </script>
</body>
</html>
