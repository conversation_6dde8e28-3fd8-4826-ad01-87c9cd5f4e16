# 项目计算方法说明文档

## 概述

本文档详细说明了PV+储能项目分析系统中所有参数的计算方法、数据源和引用规范。所有数据必须有且仅有一个计算来源，所有需要用到该数据的地方都必须直接引用这个唯一的数据源。

## 数据计算层次结构

### 1. 基础数据层（小时数据）
- **数据源**: `ProjectHourlyData[]` (8760个小时数据点)
- **计算位置**: `src/services/projectAnalysisService.ts` 中的 `calculateHourlyData` 函数
- **存储位置**: `project.analysisResults.hourlyData`

### 2. 聚合数据层
- **日数据**: 由小时数据聚合计算得出
- **月数据**: 由小时数据聚合计算得出
- **年数据**: 由小时数据聚合计算得出

## 核心参数计算方法

### 光伏发电量 (PV Generation)

#### 小时发电量计算
- **计算函数**: `calculateHourlyData` in `projectAnalysisService.ts`
- **计算公式**:
  ```
  每组光伏小时发电量 = 组件功率 × 数量 × 光照强度 × 系统效率 × 逆变器效率
  ```
- **数据结构**: `hour.pvGeneration[moduleId]` (按组件ID分别存储)
- **引用规范**: 所有需要光伏发电量的地方必须从 `hourlyData` 中获取

#### 聚合发电量计算
- **日发电量**: `Object.values(hour.pvGeneration).reduce((sum, val) => sum + val, 0)` 对当日所有小时求和
- **月发电量**: 对当月所有小时求和
- **年发电量**: 对全年所有小时求和
- **存储位置**: `project.analysisResults.yearlyData.pvGeneration`

### 储能充放电量 (Storage Charge/Discharge)

#### 小时充放电计算
- **计算函数**: `calculateHourlyData` in `projectAnalysisService.ts`
- **数据结构**: `hour.storageCharge` (正值表示充电，负值表示放电)
- **计算逻辑**: 基于光伏发电量、用电量和储能策略

#### 聚合充放电量计算
- **年充电量**: `hourlyData.filter(h => h.storageCharge > 0).reduce((sum, h) => sum + h.storageCharge, 0)`
- **年放电量**: `hourlyData.filter(h => h.storageCharge < 0).reduce((sum, h) => sum + Math.abs(h.storageCharge), 0)`

### 电网交互量 (Grid Import/Export)

#### 小时电网交互计算
- **计算函数**: `calculateHourlyData` in `projectAnalysisService.ts`
- **数据结构**:
  - `hour.gridImport`: 从电网购电量
  - `hour.gridExport`: 向电网售电量
- **计算逻辑**: 基于供需平衡和储能状态

#### 聚合电网交互量
- **年购电量**: `hourlyData.reduce((sum, h) => sum + h.gridImport, 0)`
- **年售电量**: `hourlyData.reduce((sum, h) => sum + h.gridExport, 0)`

### 经济效益计算

#### 光伏收益 (PV Benefit)
- **唯一计算源**: `calculateYearlyData` in `projectAnalysisService.ts` (第728-753行)
- **计算公式**:
  ```javascript
  for (const hour of hourlyData) {
    const totalHourPvGeneration = Object.values(hour.pvGeneration).reduce((sum, val) => sum + val, 0);
    if (totalHourPvGeneration <= hour.electricityConsumption) {
      // 全部自用
      hourlyPvBenefit = totalHourPvGeneration * hour.electricityPrice;
    } else {
      // 部分自用，部分上网
      hourlyPvBenefit = hour.electricityConsumption * hour.electricityPrice +
                       (totalHourPvGeneration - hour.electricityConsumption) * hour.gridFeedInPrice;
    }
    pvBenefit += hourlyPvBenefit;
  }
  ```
- **存储位置**: `project.analysisResults.yearlyData.pvBenefit`
- **引用规范**: 所有显示光伏收益的地方必须使用 `yearlyData.pvBenefit`

#### 储能收益 (Storage Benefit)
- **唯一计算源**: `calculateYearlyData` in `projectAnalysisService.ts` (第772-787行)
- **计算公式**（与StorageAnalysisChart中的计算完全一致）:
  ```javascript
  for (const hour of hourlyData) {
    let hourlyBenefit = 0;
    if (hour.storageCharge > 0) {
      // 充电时，收益为负（使用上网电价）
      hourlyBenefit = -hour.gridFeedInPrice * hour.storageCharge;
    } else if (hour.storageCharge < 0) {
      // 放电时，收益为正（使用用电价格）
      hourlyBenefit = hour.electricityPrice * Math.abs(hour.storageCharge);
    }
    storageBenefit += hourlyBenefit;
  }
  ```
- **存储位置**: `project.analysisResults.yearlyData.storageBenefit`
- **引用规范**: 所有显示储能收益的地方必须使用 `yearlyData.storageBenefit`
- **重要说明**: 此计算方法与StorageAnalysisChart组件中的计算逻辑完全一致，确保数据一致性

#### 总收益 (Total Benefit)
- **唯一计算源**: `calculateYearlyData` in `projectAnalysisService.ts` (第795行)
- **计算公式**: `totalBenefit = pvBenefit + storageBenefit`
- **存储位置**: `project.analysisResults.yearlyData.totalBenefit`
- **引用规范**: 所有显示总收益的地方必须使用 `yearlyData.totalBenefit`

#### 年购电费用 (Annual Electricity Cost)
- **唯一计算源**: `calculateYearlyData` in `projectAnalysisService.ts` (第778-783行)
- **计算公式**: `annualElectricityCost = hourlyData.reduce((sum, hour) => sum + hour.gridImport * hour.electricityPrice, 0)`
- **存储位置**: `project.analysisResults.yearlyData.annualElectricityCost`

### 投资回报指标

#### 总投资成本 (Total Investment)
- **计算公式**:
  ```javascript
  const pvModulesCost = project.pvModules.reduce((sum, module) => sum + module.price * module.quantity, 0);
  const energyStorageCost = project.energyStorage.reduce((sum, storage) => sum + storage.price * storage.quantity, 0);
  const invertersCost = project.inverters.reduce((sum, inverter) => sum + inverter.price * inverter.quantity, 0);
  const otherInvestmentsCost = project.otherInvestments.reduce((sum, item) => sum + item.price, 0);
  const totalInvestment = pvModulesCost + energyStorageCost + invertersCost + otherInvestmentsCost;
  ```
- **计算位置**: 各个组件中直接计算（无需存储，实时计算）

#### 投资回报率 (ROI)
- **唯一计算源**: `calculateYearlyData` in `projectAnalysisService.ts` (第806行)
- **计算公式**: `roi = (totalBenefit / totalInvestment) * 100`
- **存储位置**: `project.analysisResults.yearlyData.roi`
- **引用规范**: 所有显示ROI的地方必须使用 `yearlyData.roi`

#### 投资回收期 (Payback Period)
- **唯一计算源**: `calculateYearlyData` in `projectAnalysisService.ts` (第807行)
- **计算公式**: `paybackPeriod = totalInvestment / totalBenefit`
- **存储位置**: `project.analysisResults.yearlyData.paybackPeriod`
- **引用规范**: 所有显示回收期的地方必须使用 `yearlyData.paybackPeriod`

## 数据引用规范

### 禁止重复计算
1. **严禁**在任何组件中重新计算已有的年度数据
2. **严禁**在不同地方使用不同的计算公式计算同一参数
3. **严禁**在前端组件中进行复杂的业务逻辑计算

### 正确引用方式
1. **年度数据**: 直接使用 `project.analysisResults.yearlyData.xxx`
2. **月度数据**: 直接使用 `project.analysisResults.monthlyData`
3. **日数据**: 直接使用 `project.analysisResults.dailyData`
4. **小时数据**: 直接使用 `project.analysisResults.hourlyData`

### 组件数据获取示例
```javascript
// 正确方式 - 直接引用年度数据
const totalBenefit = project.analysisResults?.yearlyData?.totalBenefit || 0;
const pvBenefit = project.analysisResults?.yearlyData?.pvBenefit || 0;
const storageBenefit = project.analysisResults?.yearlyData?.storageBenefit || 0;
const roi = project.analysisResults?.yearlyData?.roi || 0;

// 错误方式 - 重新计算
// const totalBenefit = pvBenefit + storageBenefit; // 禁止！
```

## 当前发现的问题

### 1. StorageAnalysisTab中的重复计算
- **问题**: 在 `StorageAnalysisTab.tsx` 中重新计算储能收益
- **位置**: 第552-587行
- **解决方案**: 直接使用 `yearlyData.storageBenefit`

### 2. PVAnalysisTab中的重复计算
- **问题**: 在 `PVAnalysisTab.tsx` 中重新计算光伏收益
- **位置**: 第244-263行
- **解决方案**: 基于 `yearlyData.pvBenefit` 按比例分配

### 3. 投资成本计算分散
- **问题**: 在多个组件中重复计算投资成本
- **解决方案**: 创建统一的投资成本计算工具函数

## 修复计划

1. 创建统一的数据计算工具函数
2. 修复所有重复计算的组件
3. 确保所有显示数据都引用唯一数据源
4. 添加数据一致性验证机制

## 开发规范

### 新增功能规范
1. **禁止**在组件中进行业务逻辑计算
2. **必须**使用已有的数据源
3. 如需新的计算参数，**必须**在 `projectAnalysisService.ts` 中添加
4. **必须**将新参数存储在相应的数据结构中

### 代码审查要点
1. 检查是否有重复的计算逻辑
2. 检查是否正确引用数据源
3. 检查计算公式是否与文档一致
4. 检查数据精度是否符合要求（3位小数）

## 统一数据计算工具函数

### 投资成本计算工具
- **函数位置**: `src/utils/investmentCalculator.ts`
- **函数名**: `calculateTotalInvestment`
- **用途**: 统一计算项目总投资成本
- **引用规范**: 所有需要投资成本的地方必须使用此函数

### 数据验证工具
- **函数位置**: `src/utils/dataValidator.ts`
- **函数名**: `validateAnalysisResults`
- **用途**: 验证分析结果数据的一致性
- **使用时机**: 分析完成后和数据显示前

## 强制执行规则

1. **代码审查必检项**:
   - 是否存在重复计算
   - 是否正确引用数据源
   - 计算公式是否一致

2. **开发阶段检查**:
   - 新增计算逻辑必须在 `projectAnalysisService.ts` 中
   - 新增显示数据必须引用已有数据源
   - 禁止在组件中进行复杂计算

3. **测试验证**:
   - 同一参数在不同位置显示的数值必须一致
   - 数据精度必须符合要求
   - 计算结果必须可重现
