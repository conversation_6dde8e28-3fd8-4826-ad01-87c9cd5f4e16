/**
 * 储能收益计算测试工具
 * 验证修复后的储能收益计算是否与StorageAnalysisChart一致
 */

import { ProjectData, ProjectHourlyData } from '../types/projectData';

/**
 * 使用StorageAnalysisChart的逻辑计算储能收益
 * @param hourlyData 小时数据
 * @returns 储能收益
 */
export const calculateStorageBenefitLikeChart = (hourlyData: ProjectHourlyData[]): number => {
  let storageBenefit = 0;

  for (const hour of hourlyData) {
    let hourlyBenefit = 0;
    if (hour.storageCharge > 0) {
      // 充电时，收益为负（使用上网电价）
      hourlyBenefit = -hour.gridFeedInPrice * hour.storageCharge;
    } else if (hour.storageCharge < 0) {
      // 放电时，收益为正（使用用电价格）
      hourlyBenefit = hour.electricityPrice * Math.abs(hour.storageCharge);
    }
    storageBenefit += hourlyBenefit;
  }

  return storageBenefit;
};

/**
 * 测试储能收益计算一致性
 * @param project 项目数据
 * @returns 测试结果
 */
export const testStorageBenefitConsistency = (project: ProjectData): {
  success: boolean;
  chartCalculation: number;
  yearlyDataValue: number;
  difference: number;
  message: string;
} => {
  const hourlyData = project.analysisResults?.hourlyData || [];
  const yearlyData = project.analysisResults?.yearlyData;

  if (!yearlyData || hourlyData.length === 0) {
    return {
      success: false,
      chartCalculation: 0,
      yearlyDataValue: 0,
      difference: 0,
      message: '缺少分析结果数据'
    };
  }

  // 使用图表逻辑计算储能收益
  const chartCalculation = calculateStorageBenefitLikeChart(hourlyData);
  
  // 获取年度数据中的储能收益
  const yearlyDataValue = yearlyData.storageBenefit;
  
  // 计算差异
  const difference = Math.abs(chartCalculation - yearlyDataValue);
  
  // 判断是否一致（允许1日元的误差）
  const success = difference <= 1.0;
  
  const message = success 
    ? '✅ 储能收益计算一致'
    : `❌ 储能收益计算不一致，差异: ${difference.toFixed(2)}日元`;

  return {
    success,
    chartCalculation,
    yearlyDataValue,
    difference,
    message
  };
};

/**
 * 生成储能收益计算详细报告
 * @param project 项目数据
 * @returns 详细报告
 */
export const generateStorageBenefitReport = (project: ProjectData): string => {
  const result = testStorageBenefitConsistency(project);
  const hourlyData = project.analysisResults?.hourlyData || [];
  
  let report = `# 储能收益计算报告\n\n`;
  report += `**项目**: ${project.name}\n`;
  report += `**测试时间**: ${new Date().toLocaleString()}\n\n`;
  
  report += `## 计算结果对比\n\n`;
  report += `- **图表计算逻辑结果**: ¥${result.chartCalculation.toFixed(2)}\n`;
  report += `- **年度数据存储值**: ¥${result.yearlyDataValue.toFixed(2)}\n`;
  report += `- **差异**: ¥${result.difference.toFixed(2)}\n`;
  report += `- **状态**: ${result.message}\n\n`;
  
  // 统计充放电数据
  let totalChargeAmount = 0;
  let totalDischargeAmount = 0;
  let chargeBenefit = 0;
  let dischargeBenefit = 0;
  
  for (const hour of hourlyData) {
    if (hour.storageCharge > 0) {
      totalChargeAmount += hour.storageCharge;
      chargeBenefit += -hour.gridFeedInPrice * hour.storageCharge;
    } else if (hour.storageCharge < 0) {
      totalDischargeAmount += Math.abs(hour.storageCharge);
      dischargeBenefit += hour.electricityPrice * Math.abs(hour.storageCharge);
    }
  }
  
  report += `## 储能运行统计\n\n`;
  report += `- **年充电量**: ${totalChargeAmount.toFixed(1)} kWh\n`;
  report += `- **年放电量**: ${totalDischargeAmount.toFixed(1)} kWh\n`;
  report += `- **充电成本**: ¥${Math.abs(chargeBenefit).toFixed(1)} (负收益)\n`;
  report += `- **放电收益**: ¥${dischargeBenefit.toFixed(1)}\n`;
  report += `- **净收益**: ¥${(dischargeBenefit + chargeBenefit).toFixed(1)}\n\n`;
  
  // 计算平均电价
  const avgChargePrice = totalChargeAmount > 0 ? Math.abs(chargeBenefit) / totalChargeAmount : 0;
  const avgDischargePrice = totalDischargeAmount > 0 ? dischargeBenefit / totalDischargeAmount : 0;
  
  report += `## 平均电价分析\n\n`;
  report += `- **平均充电电价**: ¥${avgChargePrice.toFixed(2)}/kWh\n`;
  report += `- **平均放电电价**: ¥${avgDischargePrice.toFixed(2)}/kWh\n`;
  report += `- **峰谷电价差**: ¥${(avgDischargePrice - avgChargePrice).toFixed(2)}/kWh\n\n`;
  
  if (!result.success) {
    report += `## 问题分析\n\n`;
    report += `储能收益计算不一致，可能的原因：\n`;
    report += `1. projectAnalysisService.ts中的计算逻辑与StorageAnalysisChart不同\n`;
    report += `2. 电价数据获取方式不一致\n`;
    report += `3. 数据精度处理差异\n`;
    report += `4. 小时数据中的电价字段缺失或错误\n\n`;
  }
  
  return report;
};

/**
 * 批量测试多个项目的储能收益计算
 * @param projects 项目数组
 * @returns 批量测试结果
 */
export const batchTestStorageBenefit = (projects: ProjectData[]): {
  totalProjects: number;
  consistentProjects: number;
  inconsistentProjects: number;
  results: { [projectId: string]: ReturnType<typeof testStorageBenefitConsistency> };
  summary: string;
} => {
  const results: { [projectId: string]: ReturnType<typeof testStorageBenefitConsistency> } = {};
  let consistentProjects = 0;
  let inconsistentProjects = 0;
  
  projects.forEach(project => {
    const result = testStorageBenefitConsistency(project);
    results[project.id] = result;
    
    if (result.success) {
      consistentProjects++;
    } else {
      inconsistentProjects++;
    }
  });
  
  const summary = `储能收益计算测试完成: ${projects.length}个项目, ${consistentProjects}个一致, ${inconsistentProjects}个不一致`;
  
  return {
    totalProjects: projects.length,
    consistentProjects,
    inconsistentProjects,
    results,
    summary
  };
};
