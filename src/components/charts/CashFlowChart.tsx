import React from 'react';
import { Card } from 'antd';
import { useTranslation } from 'react-i18next';
import ReactECharts from 'echarts-for-react';
import { ProjectData } from '../../types/projectData';

interface CashFlowChartProps {
  project: ProjectData;
}

/**
 * 现金流图组件
 */
const CashFlowChart: React.FC<CashFlowChartProps> = ({ project }) => {
  const { t } = useTranslation();

  // 获取年度数据
  const yearlyData = project.analysisResults?.yearlyData;
  if (!yearlyData) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '50px' }}>
          {t('analysis.noData')}
        </div>
      </Card>
    );
  }

  // 计算项目实施前的年购电费用
  // 这里需要计算如果没有光伏和储能系统，全部用电量都从电网购买的费用
  // 使用总用电量 * 平均电价来估算
  const totalConsumption = yearlyData.electricityConsumption || 0;
  const averagePrice = totalConsumption > 0 ? (yearlyData.annualElectricityCost || 0) / (yearlyData.gridImport || 1) : 25;
  const beforeProjectCost = totalConsumption * averagePrice;

  // 计算项目实施后的年购电费用（实际从电网购买的电量费用）
  const afterProjectCost = yearlyData.annualElectricityCost || 0;

  const chartData = [
    {
      name: t('analysis.beforeProject'),
      value: beforeProjectCost,
      itemStyle: { color: '#f5222d' }
    },
    {
      name: t('analysis.afterProject'),
      value: afterProjectCost,
      itemStyle: { color: '#52c41a' }
    }
  ];

  const option = {
    title: {
      text: t('analysis.cashFlowChart'),
      left: 'center',
      top: 20,
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function(params: any) {
        const data = params[0];
        const savings = beforeProjectCost - afterProjectCost;
        const savingsText = savings > 0 ? `<br/>年节省: JPY ${savings.toLocaleString()}` : '';
        return `${data.name}<br/>JPY ${data.value.toLocaleString()}${savingsText}`;
      }
    },
    xAxis: {
      type: 'category',
      data: chartData.map(item => item.name),
      axisLabel: {
        interval: 0,
        rotate: 0
      }
    },
    yAxis: {
      type: 'value',
      name: '年费用 (JPY)',
      axisLabel: {
        formatter: function(value: number) {
          if (value >= 1000000) {
            return (value / 1000000).toFixed(1) + 'M';
          } else if (value >= 1000) {
            return (value / 1000).toFixed(1) + 'K';
          }
          return value.toString();
        }
      }
    },
    series: [
      {
        name: '年费用',
        type: 'bar',
        data: chartData,
        barWidth: '60%',
        label: {
          show: true,
          position: 'top',
          formatter: function(params: any) {
            return `JPY ${params.value.toLocaleString()}`;
          }
        },
        markLine: {
          data: [
            {
              type: 'average',
              name: '平均值',
              lineStyle: {
                color: '#1890ff',
                type: 'dashed'
              }
            }
          ]
        }
      }
    ],
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    }
  };

  return (
    <Card>
      <ReactECharts
        option={option}
        style={{ height: '400px', width: '100%' }}
        opts={{ renderer: 'canvas' }}
      />
      <div style={{ textAlign: 'center', marginTop: '10px', color: '#666' }}>
        {beforeProjectCost > afterProjectCost && (
          <span>
            年节省费用: JPY {(beforeProjectCost - afterProjectCost).toLocaleString()}
            ({(((beforeProjectCost - afterProjectCost) / beforeProjectCost) * 100).toFixed(1)}%)
          </span>
        )}
      </div>
    </Card>
  );
};

export default CashFlowChart;
