import React from 'react';
import { Card } from 'antd';
import { useTranslation } from 'react-i18next';
import ReactECharts from 'echarts-for-react';
import { ProjectData } from '../../types/projectData';

interface BenefitPieChartProps {
  project: ProjectData;
}

/**
 * 收益饼图组件
 */
const BenefitPieChart: React.FC<BenefitPieChartProps> = ({ project }) => {
  const { t } = useTranslation();

  // 获取年度收益数据
  const yearlyData = project.analysisResults?.yearlyData;
  if (!yearlyData) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '50px' }}>
          {t('analysis.noData')}
        </div>
      </Card>
    );
  }

  const pvBenefit = yearlyData.pvBenefit || 0;
  const storageBenefit = yearlyData.storageBenefit || 0;
  const totalBenefit = pvBenefit + storageBenefit;

  console.log('BenefitPieChart - 光伏收益(从yearlyData):', pvBenefit, '日元');
  console.log('BenefitPieChart - 储能收益(从yearlyData):', storageBenefit, '日元');
  console.log('BenefitPieChart - 总收益:', totalBenefit, '日元');

  // 验证数据源一致性 - 使用与StorageAnalysisChart相同的计算方法
  const hourlyData = project.analysisResults?.hourlyData || [];
  let calculatedStorageBenefit = 0;
  for (const hour of hourlyData) {
    let hourlyBenefit = 0;
    if (hour.storageCharge > 0) {
      // 充电时，收益为负（使用上网电价）
      hourlyBenefit = -hour.gridFeedInPrice * hour.storageCharge;
    } else if (hour.storageCharge < 0) {
      // 放电时，收益为正（使用用电价格）
      hourlyBenefit = hour.electricityPrice * Math.abs(hour.storageCharge);
    }
    calculatedStorageBenefit += hourlyBenefit;
  }
  console.log('BenefitPieChart - 重新计算的储能收益:', calculatedStorageBenefit, '日元');

  const benefitDifference = Math.abs(storageBenefit - calculatedStorageBenefit);
  if (benefitDifference > 1.0) {
    console.error('❌ BenefitPieChart - 储能收益数据不一致!', {
      yearlyData: storageBenefit,
      calculated: calculatedStorageBenefit,
      difference: benefitDifference
    });
  } else {
    console.log('✅ BenefitPieChart - 储能收益数据一致');
  }

  // 准备饼图数据
  const benefitData = [
    {
      value: pvBenefit,
      name: t('analysis.pvBenefitShort'),
      itemStyle: { color: '#1890ff' }
    },
    {
      value: storageBenefit,
      name: t('analysis.storageBenefitShort'),
      itemStyle: { color: '#52c41a' }
    }
  ].filter(item => item.value > 0); // 只显示有收益的项目

  const option = {
    title: {
      text: t('analysis.benefitBreakdown'),
      left: 'center',
      top: 20,
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: function(params: any) {
        const percentage = totalBenefit > 0 ? ((params.value / totalBenefit) * 100).toFixed(1) : '0.0';
        return `${params.name}<br/>JPY ${params.value.toLocaleString()}<br/>${percentage}%`;
      }
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      top: 'middle',
      formatter: function(name: string) {
        const item = benefitData.find(d => d.name === name);
        if (item && totalBenefit > 0) {
          const percentage = ((item.value / totalBenefit) * 100).toFixed(1);
          return `${name} (${percentage}%)`;
        }
        return name;
      }
    },
    series: [
      {
        name: t('analysis.benefitBreakdown'),
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['60%', '50%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold',
            formatter: function(params: any) {
              const percentage = totalBenefit > 0 ? ((params.value / totalBenefit) * 100).toFixed(1) : '0.0';
              return `${params.name}\nJPY ${params.value.toLocaleString()}\n${percentage}%`;
            }
          }
        },
        labelLine: {
          show: false
        },
        data: benefitData
      }
    ]
  };

  return (
    <Card>
      <ReactECharts
        option={option}
        style={{ height: '400px', width: '100%' }}
        opts={{ renderer: 'canvas' }}
      />
    </Card>
  );
};

export default BenefitPieChart;
