import React from 'react';
import { Card } from 'antd';
import { useTranslation } from 'react-i18next';
import ReactECharts from 'echarts-for-react';
import { ProjectData } from '../../types/projectData';

interface BenefitPieChartProps {
  project: ProjectData;
}

/**
 * 收益饼图组件
 */
const BenefitPieChart: React.FC<BenefitPieChartProps> = ({ project }) => {
  const { t } = useTranslation();

  // 获取年度收益数据
  const yearlyData = project.analysisResults?.yearlyData;
  if (!yearlyData) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '50px' }}>
          {t('analysis.noData')}
        </div>
      </Card>
    );
  }

  const pvBenefit = yearlyData.pvBenefit || 0;
  const storageBenefit = yearlyData.storageBenefit || 0;
  const totalBenefit = pvBenefit + storageBenefit;

  // 准备饼图数据
  const benefitData = [
    {
      value: pvBenefit,
      name: t('analysis.pvBenefitShort'),
      itemStyle: { color: '#1890ff' }
    },
    {
      value: storageBenefit,
      name: t('analysis.storageBenefitShort'),
      itemStyle: { color: '#52c41a' }
    }
  ].filter(item => item.value > 0); // 只显示有收益的项目

  const option = {
    title: {
      text: t('analysis.benefitBreakdown'),
      left: 'center',
      top: 20,
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: function(params: any) {
        const percentage = totalBenefit > 0 ? ((params.value / totalBenefit) * 100).toFixed(1) : '0.0';
        return `${params.name}<br/>JPY ${params.value.toLocaleString()}<br/>${percentage}%`;
      }
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      top: 'middle',
      formatter: function(name: string) {
        const item = benefitData.find(d => d.name === name);
        if (item && totalBenefit > 0) {
          const percentage = ((item.value / totalBenefit) * 100).toFixed(1);
          return `${name} (${percentage}%)`;
        }
        return name;
      }
    },
    series: [
      {
        name: t('analysis.benefitBreakdown'),
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['60%', '50%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold',
            formatter: function(params: any) {
              const percentage = totalBenefit > 0 ? ((params.value / totalBenefit) * 100).toFixed(1) : '0.0';
              return `${params.name}\nJPY ${params.value.toLocaleString()}\n${percentage}%`;
            }
          }
        },
        labelLine: {
          show: false
        },
        data: benefitData
      }
    ]
  };

  return (
    <Card>
      <ReactECharts
        option={option}
        style={{ height: '400px', width: '100%' }}
        opts={{ renderer: 'canvas' }}
      />
    </Card>
  );
};

export default BenefitPieChart;
