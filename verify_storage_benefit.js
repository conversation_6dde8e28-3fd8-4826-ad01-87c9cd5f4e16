#!/usr/bin/env node

/**
 * 验证储能收益修复脚本
 * 使用真实的项目文件中的小时数据重新计算储能收益
 */

import fs from 'fs';
import path from 'path';

// 项目文件路径
const projectFilePath = 'server/data/projects/4d450b50-49e1-48dc-a04e-4cf81aa7268e.json';

console.log('=== 验证储能收益计算 ===');
console.log(`读取项目文件: ${projectFilePath}`);

// 读取项目文件
if (!fs.existsSync(projectFilePath)) {
    console.error('❌ 项目文件不存在:', projectFilePath);
    process.exit(1);
}

const projectData = JSON.parse(fs.readFileSync(projectFilePath, 'utf8'));
const hourlyData = projectData.analysisResults?.hourlyData || [];

console.log(`✅ 成功读取项目数据`);
console.log(`小时数据点数: ${hourlyData.length}`);

// 当前年度数据中的储能收益
const currentStorageBenefit = projectData.analysisResults?.yearlyData?.storageBenefit || 0;
console.log(`当前年度数据中的储能收益: ${currentStorageBenefit.toFixed(2)} 日元`);

// 重新计算储能收益
console.log('\n=== 重新计算储能收益 ===');
let newStorageBenefit = 0;
let chargeCount = 0;
let dischargeCount = 0;
let totalChargeAmount = 0;
let totalDischargeAmount = 0;
let chargeBenefit = 0;
let dischargeBenefit = 0;

// 统计充放电数据
for (const hour of hourlyData) {
    let hourlyBenefit = 0;

    if (hour.storageCharge > 0) {
        // 充电时，收益为负（使用上网电价）
        hourlyBenefit = -hour.gridFeedInPrice * hour.storageCharge;
        chargeCount++;
        totalChargeAmount += hour.storageCharge;
        chargeBenefit += hourlyBenefit;
    } else if (hour.storageCharge < 0) {
        // 放电时，收益为正（使用用电价格）
        hourlyBenefit = hour.electricityPrice * Math.abs(hour.storageCharge);
        dischargeCount++;
        totalDischargeAmount += Math.abs(hour.storageCharge);
        dischargeBenefit += hourlyBenefit;
    }

    newStorageBenefit += hourlyBenefit;
}

console.log(`储能充电小时数: ${chargeCount}`);
console.log(`储能放电小时数: ${dischargeCount}`);
console.log(`总充电量: ${totalChargeAmount.toFixed(2)} kWh`);
console.log(`总放电量: ${totalDischargeAmount.toFixed(2)} kWh`);
console.log(`充电收益（负值）: ${chargeBenefit.toFixed(2)} 日元`);
console.log(`放电收益（正值）: ${dischargeBenefit.toFixed(2)} 日元`);
console.log(`重新计算的储能收益: ${newStorageBenefit.toFixed(2)} 日元`);

// 比较结果
const difference = Math.abs(newStorageBenefit - currentStorageBenefit);
console.log(`\n=== 比较结果 ===`);
console.log(`当前储能收益: ${currentStorageBenefit.toFixed(2)} 日元`);
console.log(`重新计算收益: ${newStorageBenefit.toFixed(2)} 日元`);
console.log(`差异: ${difference.toFixed(2)} 日元`);

if (difference > 1.0) {
    console.log('❌ 储能收益数据不一致，需要修复');

    // 更新项目数据
    console.log('\n=== 更新项目数据 ===');
    projectData.analysisResults.yearlyData.storageBenefit = newStorageBenefit;
    projectData.analysisResults.yearlyData.totalBenefit =
        projectData.analysisResults.yearlyData.pvBenefit + newStorageBenefit;
    projectData.analysisResults.yearlyData.renewableEnergyBenefit =
        projectData.analysisResults.yearlyData.pvBenefit + newStorageBenefit;

    // 保存修复后的数据
    fs.writeFileSync(projectFilePath, JSON.stringify(projectData, null, 2));
    console.log('✅ 项目数据已更新并保存');

    console.log(`更新后的储能收益: ${projectData.analysisResults.yearlyData.storageBenefit.toFixed(2)} 日元`);
    console.log(`更新后的总收益: ${projectData.analysisResults.yearlyData.totalBenefit.toFixed(2)} 日元`);
} else {
    console.log('✅ 储能收益数据一致，无需修复');
}

// 验证一些样本小时数据
console.log('\n=== 样本小时数据验证 ===');
const sampleHours = hourlyData.filter(h => h.storageCharge !== 0).slice(0, 5);
for (const hour of sampleHours) {
    const benefit = hour.storageCharge > 0
        ? -hour.gridFeedInPrice * hour.storageCharge
        : hour.electricityPrice * Math.abs(hour.storageCharge);
    console.log(`${hour.month}月${hour.day}日${hour.hour}时: 储能充放电=${hour.storageCharge.toFixed(3)}kWh, 收益=${benefit.toFixed(2)}日元`);
}

console.log('\n=== 验证完成 ===');
